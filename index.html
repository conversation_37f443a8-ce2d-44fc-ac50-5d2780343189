<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Snap App</title>
  <!-- Global styles remain as external CSS -->
  <link rel="stylesheet" href="snapapp.css">
  
  <!-- Chart System Styles -->
  <link rel="stylesheet" href="components/charts/snap-charts.css">
</head>
<body>
  <!-- The entire app structure will be created by JavaScript -->
  
  <!-- Timezone Utility Script (must load first) -->
  <script src="utils/timezone.js"></script>

  <!-- Chart System Script -->
  <script src="components/charts/snap-charts.js"></script>

  <!-- Main application script that contains embedded HTML -->
  <script src="snapapp.js"></script>
</body>
</html> 