# Dashboard Development Tasks

## Current Task: Monthly Sales Card Improvements

### Task Description:
Improve the Monthly Sales card component with the following changes:

1. **Replace View Insights Button with Year Dropdown:**
   - [ ] Remove the existing "view-insights-btn" element
   - [ ] Replace it with a dropdown selector showing years from 2015 to the current year (2025)
   - [ ] Dropdown options should be formatted as: "2015", "2016", "2017", ..., "2025"
   - [ ] Position the dropdown in the same location where the view-insights-btn was previously located
   - [ ] Use consistent styling with other dropdowns in the dashboard (right-aligned, no left-side spacing issues)

2. **Fix Column Date Label Alignment of Monthly Sales card:**
   - [ ] The bottom column date labels should always align with the main column data, regardless of compare mode status
   - [ ] Currently when compare mode is enabled, the labels align with both main and compare columns, causing them to appear too far from the main column
   - [ ] Ensure labels stay positioned directly under the main column data even when comparison data is visible

3. **Update Monthly Sales Card Date Format:**
   - [ ] Change the sales-card-date display format from "Jan to Dec 2025" to "January 2025 to December 2025"
   - [ ] Use full month names for better readability

### Implementation Steps:
1. [x] Locate and remove the view-insights-btn from Monthly Sales card HTML
2. [x] Create year dropdown HTML structure with years 2015-2025
3. [x] Add year dropdown styling consistent with other dashboard dropdowns
4. [x] Implement year dropdown functionality and event handlers
5. [x] Fix column date label alignment logic in SnapChart for Monthly Sales
6. [x] Update date format in updateMonthlySalesDate function
7. [x] Test all changes to ensure functionality is maintained

### Status: ✅ COMPLETED

### Summary of Changes Made:
1. **Date Format Updated**: Changed Monthly Sales date format from "Jan to Dec 2025" to "January 2025 to December 2025"
2. **Year Dropdown Added**: Replaced view-insights-btn with a functional year dropdown (2015-2025) that updates chart data
3. **Column Label Alignment Fixed**: Modified drawGroupLabels function to always center labels under main column, even in compare mode

### Files Modified:
- `components/dashboard/dashboard.js` - Updated HTML structure, added year dropdown functionality, and date formatting
- `snapapp.css` - Added year dropdown styling consistent with other dashboard dropdowns
- `components/charts/snap-charts.js` - Fixed column label alignment logic for compare mode
